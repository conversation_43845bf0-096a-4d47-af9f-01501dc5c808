import React, {ReactNode} from 'react';
import {StyleSheet, TouchableOpacity, View, Text} from 'react-native';
import {useTheme} from 'styled-components/native';
import GlobalStyles from '@/constants/GlobalStyles';
import {ChevronRightIcon} from 'react-native-heroicons/solid';
import {ITheme} from '@/styles/themes';

export type SettingsItemProps = {
  title: string;
  rightElement?: ReactNode;
  leftIcon?: ReactNode;
  onPress?: () => void;
  disabled?: boolean;
  showDivider?: boolean;
};

const SettingsItem: React.FC<SettingsItemProps> = ({
  title,
  rightElement,
  leftIcon,
  onPress,
  disabled = false,
}) => {
  const Container = onPress ? TouchableOpacity : View;
  const currentTheme = useTheme() as ITheme;
  const styles = createStyles(currentTheme);

  return (
    <>
      <Container
        style={styles.row}
        onPress={onPress}
        disabled={disabled}
        {...(onPress && {
          accessibilityRole: 'button',
          accessibilityLabel: title,
          tabIndex: 0,
        })}
      >
        {leftIcon && <View style={styles.leftIconContainer}>{leftIcon}</View>}
        <Text
          style={[styles.titleText, leftIcon ? styles.titleWithIcon : null]}
          numberOfLines={1}
        >
          {title}
        </Text>
        <View style={styles.valueView}>
          {onPress ? (
            <ChevronRightIcon size={24} fill={currentTheme.colors.secondary} />
          ) : (
            rightElement
          )}
        </View>
      </Container>
    </>
  );
};

const createStyles = (currentTheme: ITheme) =>
  StyleSheet.create({
    row: {
      flexDirection: 'row',
      alignSelf: 'center',
      justifyContent: 'space-between',
      width: '90%',
      marginTop: 20,
      marginBottom: 12,
      alignItems: 'center',
    },
    titleText: {
      flex: 1,
      fontSize: 16,
      fontFamily: GlobalStyles.fonts.sfPro,
      fontWeight: '500',
      lineHeight: 19,
      color: currentTheme.colors.text,
    },
    titleWithIcon: {
      marginLeft: 12,
    },
    leftIconContainer: {
      marginRight: 4,
    },
    valueView: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
  });

export default SettingsItem;
