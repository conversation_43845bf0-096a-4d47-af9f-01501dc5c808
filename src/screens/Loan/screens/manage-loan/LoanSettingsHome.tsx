import React, {memo} from 'react';
import {useTranslation} from 'react-i18next';
import {<PERSON>ert, ScrollView, StyleSheet, Switch, View} from 'react-native';
import {
  ArrowRightOnRectangleIcon,
  EnvelopeIcon,
  LockClosedIcon,
  MoonIcon,
  SunIcon,
  TrashIcon,
} from 'react-native-heroicons/solid';
import {useDispatch} from 'react-redux';

import GlobalStyles from '@/constants/GlobalStyles';
import {useCommon} from '@/hooks/redux';
import {navigationRef} from '@/navigation/utils/navigation';
import SettingsItem from '@/screens/SettingsScreen/ui/SettingsItem';
import SettingsSection from '@/screens/SettingsScreen/ui/SettingsSection';
import {setAppTheme} from '@/storage/actions/common';
import {setAccessToken} from '@/storage/actions/loanActions';
import theme from '@/styles/themes';
import SafeAreaInset from '@components/SafeAreaInset';

const LoanSettingsHome = () => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {appTheme} = useCommon();
  const isDark = appTheme === 'dark';

  const setThemeMode = (theme: 'light' | 'dark') => {
    dispatch(setAppTheme(theme));
  };

  const renderThemeSwitch = () => (
    <View style={styles.switchView}>
      <SunIcon
        size={20}
        fill={isDark ? GlobalStyles.gray.gray600 : GlobalStyles.orange.orange500}
      />
      <Switch
        trackColor={{
          false: GlobalStyles.gray.gray600,
          true: GlobalStyles.primary.primary400,
        }}
        thumbColor={GlobalStyles.base.white}
        ios_backgroundColor={GlobalStyles.gray.gray600}
        onValueChange={() => {
          setThemeMode(isDark ? 'light' : 'dark');
        }}
        value={isDark}
      />
      <MoonIcon
        size={20}
        fill={isDark ? GlobalStyles.primary.primary400 : GlobalStyles.gray.gray600}
      />
    </View>
  );

  const handleLogout = async () => {
    try {
      dispatch(setAccessToken(''));
      navigationRef.current?.reset({
        index: 0,
        routes: [{name: 'LoanOnboarding'}],
      });
    } catch (error) {
      console.error('[LoanSettingsHome] Error during logout:', error);
      navigationRef.current?.reset({
        index: 0,
        routes: [{name: 'LoanOnboarding'}],
      });
    }
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      t('settings.deleteAccountMessage'),
      'This will permanently delete your account and all loan data. This action cannot be undone.',
      [
        {
          text: t('settings.no'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: t('settings.yes'),
          onPress: () => {
            handleLogout();
          },
          style: 'destructive',
        },
      ],
      {cancelable: false},
    );
  };

  const handleLogoutConfirmation = () => {
    Alert.alert(
      t('settings.logoutMessage'),
      'You will be logged out of your loan account.',
      [
        {
          text: t('settings.no'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: t('settings.yes'),
          onPress: () => {
            handleLogout();
          },
        },
      ],
      {cancelable: false},
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <SettingsSection title="Appearance">
            <SettingsItem
              title="Theme"
              leftIcon={<MoonIcon size={26} fill={GlobalStyles.gray.gray700} />}
              rightElement={renderThemeSwitch()}
            />
          </SettingsSection>

          <SettingsSection title="Security">
            <SettingsItem
              title="Two-Factor Authentication (2FA)"
              onPress={() => {
                navigationRef.current?.navigate('LoanTwoFactorAuth');
              }}
              leftIcon={<LockClosedIcon size={26} fill={GlobalStyles.gray.gray700} />}
            />
            <SettingsItem
              title="Change Email"
              onPress={() => {
                navigationRef.current?.navigate('LoanChangeEmail');
              }}
              leftIcon={<EnvelopeIcon size={26} fill={GlobalStyles.gray.gray700} />}
            />
            <SettingsItem
              title="Change Password"
              onPress={() => {
                navigationRef.current?.navigate('LoanChangePassword');
              }}
              leftIcon={<LockClosedIcon size={26} fill={GlobalStyles.gray.gray700} />}
            />
          </SettingsSection>

          <SettingsSection title={'Account'} isLast>
            <SettingsItem
              title={'Logout'}
              onPress={handleLogoutConfirmation}
              leftIcon={
                <ArrowRightOnRectangleIcon size={26} fill={GlobalStyles.gray.gray700} />
              }
            />
            <SettingsItem
              title={'Delete Account'}
              onPress={handleDeleteAccount}
              leftIcon={<TrashIcon size={26} fill={GlobalStyles.error.error600} />}
            />
          </SettingsSection>

          <SafeAreaInset type="bottom" />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 8,
    flexDirection: 'column',
  },
  content: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
  },
  accountItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 19,
    marginBottom: 32,
    width: '100%',
  },
  accountIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: GlobalStyles.orange.orange500,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  accountInfo: {
    flex: 1,
  },
  themeContainer: {
    paddingHorizontal: 19,
    paddingVertical: 16,
  },
  switchView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 16,
  },
  accountTitle: {
    fontSize: 17,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '600',
    lineHeight: 22,
    color: GlobalStyles.gray.gray900,
    marginBottom: 2,
  },
  accountSubtitle: {
    fontSize: 15,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '400',
    lineHeight: 20,
    color: GlobalStyles.gray.gray800,
  },
  valueText: {
    fontSize: 16,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
    lineHeight: 19,
    color: GlobalStyles.gray.gray900,
    marginHorizontal: 2,
    alignSelf: 'center',
  },
});

export default memo(LoanSettingsHome);
