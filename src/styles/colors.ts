export const baseColors = {
  // Neutral/Gray Scale - Primary UI colors
  neutral: {
    50: '#fefefe',
    100: '#fafafb',
    200: '#f8f8f9',
    300: '#f5f5f6',
    400: '#f3f3f5',
    500: '#f0f0f2',
    600: '#dadadc',
    700: '#aaaaac',
    750: '#98989a',
    800: '#848485',
    900: '#656566',
    950: '#3a3a3c',
  },

  // Primary Brand Colors - Purple/Plum theme
  primary: {
    50: '#faf8fa',
    100: '#ece7eb',
    200: '#d9cdd7',
    300: '#c3b3c2',
    400: '#a58fa4',
    500: '#7c5c7b',
    600: '#633c61',
    700: '#4a2d48',
    800: '#3c0b3a',
    900: '#2b0829',
    950: '#190518',
  },

  // Secondary/Accent Colors - Orange/Coral
  secondary: {
    50: '#fef9f5',
    100: '#fef1e8',
    200: '#fce4d1',
    300: '#fcd3b8',
    400: '#fabe96',
    500: '#f8a066',
    600: '#f68d48',
    700: '#f4711a',
    800: '#de6718',
    900: '#ad5012',
    950: '#662f0b',
  },

  // Success Colors - Green
  success: {
    50: '#f0fdf9',
    100: '#ebfcf6',
    200: '#d1f7e8',
    300: '#c1f4e3',
    400: '#a3efd6',
    500: '#78e8c3',
    600: '#36dda6',
    700: '#31c997',
    800: '#269d76',
    900: '#1e7a5b',
    950: '#175d46',
  },

  // Error/Danger Colors - Red/Pink
  error: {
    50: '#fef2f4',
    100: '#ffe6ec',
    200: '#ffcdd8',
    300: '#ffb0c4',
    400: '#ff8aa7',
    500: '#ff547f',
    600: '#ff3366',
    700: '#ff0040',
    800: '#e8003a',
    900: '#b5002d',
    950: '#6b001b',
  },

  // Warning Colors - Amber/Yellow
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03',
  },

  // Info Colors - Blue
  info: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
    950: '#172554',
  },

  // Base Colors - Absolute values
  base: {
    white: '#ffffff',
    black: '#000000',
    transparent: 'transparent',
  },

  // Special Colors
  special: {
    overlay: 'rgba(0, 0, 0, 0.5)',
    overlayLight: 'rgba(0, 0, 0, 0.3)',
    overlayDark: 'rgba(0, 0, 0, 0.7)',
    shadow: 'rgba(0, 0, 0, 0.1)',
    shadowDark: 'rgba(0, 0, 0, 0.3)',
    backdrop: 'rgba(52, 52, 52, 0.8)',
  },
} as const;
