import GlobalStyles from '@/constants/GlobalStyles';
import {Dimensions} from 'react-native';
import {baseColors} from './colors';

export const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');
const [shortDimension, longDimension] =
  SCREEN_WIDTH < SCREEN_HEIGHT
    ? [SCREEN_WIDTH, SCREEN_HEIGHT]
    : [SCREEN_HEIGHT, SCREEN_WIDTH];

/* ============================================================================================== */
/*                                              HOOKS                                             */
/* ============================================================================================== */

// export const useThemeColors = () => {
//   const colorScheme = useColorScheme();
//   return colorScheme === 'dark' ? colors.dark : colors.light;
// };

/* ============================================================================================== */
/*                                              UTILS                                             */
/* ============================================================================================== */

const guidelineBaseWidth = 430;
const guidelineBaseHeight = 932;

export const scale = (size) => (shortDimension / guidelineBaseWidth) * size;
export const verticalScale = (size) => (longDimension / guidelineBaseHeight) * size;
export const moderateScale = (size, factor = 0.5) => size + (scale(size) - size) * factor;
export const moderateVerticalScale = (size, factor = 0.5) =>
  size + (verticalScale(size) - size) * factor;

/* ============================================================================================== */
/*                                            CONSTANTS                                           */
/* ============================================================================================== */

const isSmallDevice = SCREEN_WIDTH < 375 || SCREEN_HEIGHT < 670;

export const colors = {
  brand: {
    main: '#633c61',
    mainLight: '#7a4d74',
    coral: '#eb634b',
    coralLight: '#ee5339',
  },
  light: {
    background: '#FFFFFF',
    text: '#000000',
    primary: '#007AFF',
    secondary: '#34C759',
    border: '#E5E5E5',
  },
  dark: {
    background: '#000000',
    text: '#FFFFFF',
    primary: '#0A84FF',
    secondary: '#30D158',
    border: '#3A3A3A',
  },
  base: {
    white: '#FFFFFF',
    black: '#000000',
  },
  neutral: {
    500: '#656566',
  },
};

export const fonts = {
  default: 'SF-Pro',
};

export const typography = {
  xs: moderateScale(12),
  sm: moderateScale(16),
  md: moderateScale(22),
  lg: moderateScale(26),
  xl: moderateScale(30),
  xxl: moderateScale(34),
};

export const spacing = {
  sm: moderateScale(8),
  md: moderateScale(12),
  lg: moderateScale(18),
  xl: moderateScale(24),
  xxl: moderateScale(34),
  xxxl: moderateScale(40),
} as const;

export const layout = {
  images: {
    xs: moderateScale(40),
    sm: moderateScale(80),
    md: moderateScale(150),
    lg: moderateScale(200),
  },
  ph: {
    sm: moderateScale(16),
    md: moderateScale(32),
    lg: moderateScale(48),
    screen: isSmallDevice ? 32 : 48,
    footer: isSmallDevice ? spacing.md * 2 : spacing.lg * 2,
  },
  pv: {
    sm: moderateScale(12),
    md: moderateScale(24),
    lg: moderateScale(34),
    screen: isSmallDevice ? spacing.sm : spacing.sm,
  },
  gap: {
    screen: spacing.lg,
  },
  borderRadius: {
    sm: moderateScale(8),
    md: moderateScale(14),
    lg: moderateScale(20),
  },
} as const;

/* ============================================================================================== */
/*                                            AS OF NOW                                           */
/* ============================================================================================== */

export interface IDefaultColors {
  accent: string;
  success: string;
  error: string;
  transparent: string;
}

export interface IThemeColors extends IDefaultColors {
  primary: string;
  secondary: string;
  background: string;
  text: string;
  onSurface: string;
  refreshControl: string;
}

interface IFont {
  fontFamily?: string;
}

interface IFonts {
  regular: IFont;
  medium: IFont;
  semiBold: IFont;
  bold: IFont;
  extraBold: IFont;
  black: IFont;
}

interface IDefaultThemeValues {
  colors: IDefaultColors;
  fonts: IFonts;
}

export interface ITheme extends IDefaultThemeValues {
  colors: IThemeColors;
}

const defaultThemeValues: IDefaultThemeValues = {
  colors: {
    ...baseColors,
    accent: '#0000007F',
    success: '#A2BC91',
    error: '#D87682',
    transparent: 'transparent',
  },
  fonts: {
    regular: {fontFamily: 'InterTight-Regular'},
    medium: {fontFamily: 'InterTight-Medium'},
    semiBold: {fontFamily: 'InterTight-SemiBold'},
    bold: {fontFamily: 'InterTight-Bold'},
    extraBold: {fontFamily: 'InterTight-ExtraBold'},
    black: {fontFamily: 'InterTight-Black'},
  },
};

const light: ITheme = {
  ...defaultThemeValues,
  colors: {
    ...defaultThemeValues.colors,
    primary: '#121212',
    secondary: '#121212',
    text: '#000000',
    background: baseColors.base.white,
    onSurface: '#D6D6D6',
    refreshControl: '#121212',
  },
};

const dark: ITheme = {
  ...defaultThemeValues,
  colors: {
    ...defaultThemeValues.colors,
    primary: baseColors.base.white,
    secondary: GlobalStyles.gray.gray750,
    text: '#FFFFFF',
    background: '#4c4c4dff',
    onSurface: GlobalStyles.gray.gray600,
    refreshControl: '#FFFFFF',
  },
};

export const getTheme = (theme: string) => {
  return theme === 'dark' ? dark : light;
};

export default {
  SCREEN_WIDTH,
  SCREEN_HEIGHT,
  isSmallDevice,
  colors,
  fonts,
  typography,
  spacing,
  layout,
};
